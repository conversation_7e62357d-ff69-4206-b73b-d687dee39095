<template>
  <Form v-slot="{ errors }" @submit="onSubmit">
    <div class="pt-9 pb-[45px] pl-[33px] pr-[48px] min-w-[80vw] sm:min-w-[605px] w-full">
      <h5 class="text-[19px]/[22px] sm:text-[27px]/[31px] font-semibold font-raleway mb-4">
        Update Payment Method
      </h5>
      
      <div class="bg-blue-50 border border-blue-200 rounded-md p-4 mb-6">
        <div class="flex">
          <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
            </svg>
          </div>
          <div class="ml-3">
            <p class="text-sm text-blue-700">
              <strong>Current Payment Method:</strong> 
              {{ currentPaymentMethod ? `**** **** **** ${currentPaymentMethod.last_four}` : 'No payment method on file' }}
            </p>
            <p class="text-sm text-blue-700 mt-1">
              This will be completely replaced with your new payment method.
            </p>
          </div>
        </div>
      </div>

      <PaymentMethodUpdate
        :subscription-id="subscriptionId"
        :errors="errors"
        @cancel="handleCancel"
        @success="handleSuccess"
        @error="handleError"
      />

      <div v-if="errorMessage" class="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
        <p class="text-sm text-red-700">{{ errorMessage }}</p>
      </div>

      <div v-if="successMessage" class="mt-4 p-3 bg-green-50 border border-green-200 rounded-md">
        <p class="text-sm text-green-700">{{ successMessage }}</p>
      </div>
    </div>
  </Form>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted } from 'vue';
import { Form } from 'vee-validate';
import { useUser } from '@stores/auth';
import { useStripe } from '@stores/stripe';
import PaymentMethodUpdate from '@components/account/PaymentMethodUpdate.vue';
import { useToast } from 'vue-toast-notification';

export default defineComponent({
  name: 'PaymentMethodUpdateModal',
  components: {
    Form,
    PaymentMethodUpdate
  },
  props: {
    subscriptionId: {
      type: Number,
      required: true
    },
    currentPaymentMethod: {
      type: Object,
      default: null
    }
  },
  emits: ['close', 'updated'],
  setup(props, { emit }) {
    const userStore = useUser();
    const storeStripe = useStripe();
    const toast = useToast();
    
    const errorMessage = ref('');
    const successMessage = ref('');
    const isSubmitting = ref(false);

    onMounted(async () => {
      // Initialize Stripe if it's the payment method
      const paymentMethod = import.meta.env.VITE_PAYMENTS_TYPE;
      if (paymentMethod === 'stripe') {
        await storeStripe.initialStripe();
      }
    });

    const handleCancel = () => {
      emit('close');
    };

    const handleSuccess = async (data: { paymentInfo: any, subscriptionId: number }) => {
      if (isSubmitting.value) return;
      
      isSubmitting.value = true;
      errorMessage.value = '';
      successMessage.value = '';

      try {
        const result = await userStore.updatePaymentMethodHandler(data.paymentInfo, data.subscriptionId);
        
        if (result.success) {
          successMessage.value = result.message;
          toast.success('Payment method updated successfully!');
          
          // Close modal after a short delay to show success message
          setTimeout(() => {
            emit('updated');
            emit('close');
          }, 1500);
        } else {
          errorMessage.value = result.message;
          toast.error(result.message);
        }
      } catch (error) {
        console.error('Payment method update error:', error);
        errorMessage.value = 'An unexpected error occurred. Please try again.';
        toast.error('Failed to update payment method');
      } finally {
        isSubmitting.value = false;
      }
    };

    const handleError = (message: string) => {
      errorMessage.value = message;
      toast.error(message);
    };

    const onSubmit = () => {
      // This is handled by the PaymentMethodUpdate component
    };

    return {
      errorMessage,
      successMessage,
      isSubmitting,
      handleCancel,
      handleSuccess,
      handleError,
      onSubmit
    };
  }
});
</script>
