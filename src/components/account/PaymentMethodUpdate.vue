<template>
  <div class="flex flex-col gap-4">
    <div class="text-xl font-bold mb-4">Update Payment Method</div>
    
    <div class="bg-yellow-50 border border-yellow-200 rounded-md p-4 mb-4">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3">
          <p class="text-sm text-yellow-700">
            You are about to replace your current payment method with a new credit card. 
            This will completely replace your existing payment method and cannot be undone.
          </p>
        </div>
      </div>
    </div>

    <div class="w-full flex flex-col justify-center relative">
      <CustomField
        v-model="paymentData.name"
        name="cardName"
        :rules="[required]"
        placeholder="Name on card"
      />
      <span class="text-red-500 text-xs pt-1">{{ errors.cardName }}</span>
    </div>

    <Stripe v-if="paymentMethod === 'stripe'" :errors="errors"/>
    <Usaepay v-if="paymentMethod === 'usaepay'" :errors="errors"/>

    <div class="flex justify-end gap-3 mt-6">
      <button
        type="button"
        @click="$emit('cancel')"
        class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-vivid-purple"
      >
        Cancel
      </button>
      <button
        type="button"
        @click="handleSubmit"
        :disabled="isSubmitting"
        class="px-4 py-2 text-sm font-medium text-white bg-vivid-purple border border-transparent rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-vivid-purple disabled:opacity-50 disabled:cursor-not-allowed"
      >
        {{ isSubmitting ? 'Updating...' : 'Update Payment Method' }}
      </button>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed } from 'vue';
import { usePayment } from '@stores/payment';
import { useStripe } from '@stores/stripe';
import CustomField from '@components/reusable/CustomField.vue';
import Stripe from '@components/payments/Stripe.vue';
import Usaepay from '@components/payments/Usaepay.vue';
import { required } from '@utills/helpers/validation';

export default defineComponent({
  name: 'PaymentMethodUpdate',
  components: {
    CustomField,
    Stripe,
    Usaepay
  },
  props: {
    subscriptionId: {
      type: Number,
      required: true
    },
    errors: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['cancel', 'success', 'error'],
  setup(props, { emit }) {
    const paymentStore = usePayment();
    const { paymentData, getPaymentData, clearPaymentData } = paymentStore;
    const storeStripe = useStripe();
    const isSubmitting = ref(false);

    const paymentMethod = import.meta.env.VITE_PAYMENTS_TYPE;

    const handleSubmit = async () => {
      if (isSubmitting.value) return;

      // Debug: Log current payment data
      console.log('Current paymentData:', paymentData);
      console.log('paymentData.name:', paymentData.name);

      // Basic validation
      if (!paymentData.name || paymentData.name.trim().length === 0) {
        emit('error', 'Please enter the name on the card.');
        return;
      }

      if (paymentData.name.trim().length > 255) {
        emit('error', 'Cardholder name is too long.');
        return;
      }

      isSubmitting.value = true;

      try {

        // Get payment data based on payment method
        const paymentInfo = await getPaymentData();

        if (paymentInfo === 'error') {
          emit('error', 'Invalid payment information. Please check your card details and try again.');
          return;
        }

        // Additional validation for payment info
        if (!paymentInfo || (paymentMethod === 'stripe' && !paymentInfo.token)) {
          emit('error', 'Failed to process payment information. Please try again.');
          return;
        }

        // Debug: Log the payment info to see what's being sent
        console.log('Payment info being sent:', paymentInfo);

        emit('success', { paymentInfo, subscriptionId: props.subscriptionId });

      } catch (error) {
        console.error('Payment method update error:', error);
        emit('error', 'An error occurred while processing your payment method. Please try again.');
      } finally {
        isSubmitting.value = false;
      }
    };

    // Clear payment data when component is mounted
    clearPaymentData();

    return {
      paymentData,
      paymentMethod,
      isSubmitting,
      handleSubmit,
      required
    };
  }
});
</script>
