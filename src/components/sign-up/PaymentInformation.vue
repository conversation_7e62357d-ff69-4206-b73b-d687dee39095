<template>
  <button
    @click.prevent="toggleDivVisibility"
    class="font-raleway flex gap-3 items-center pl-[17px] pr-[54px] mt-[28px] cursor-pointer py-2 w-max bg-lavender rounded-[18px] text-vivid-purple relative"
  >
    <IconCoupon />
    <span class="text-[13px] sm:text-base">I have a promo/referral code</span>
    <img
      src="@assets/images/purple-arrow.png"
      alt="arrow"
      class="absolute w-[13px] h-2 right-5"
      draggable="false"
      :class="!isDivVisible ? 'rotete-180' : '-rotate-180'"
    />
  </button>
  <div v-if="isDivVisible" class="flex flex-col mt-[17px] relative">
    <CustomField
      @input=" (e: Event) => { promo = (e.target as HTMLInputElement)?.value } "
      name="promocode"
      :rules="[required]"
      placeholder="Promo Code"
      :modelValue="promo"
    />
    <button
      v-if="!couponCode"
      @click.prevent="applyDiscount"
      class="absolute top-[14px] right-4 text-lg text-vivid-purple">
      Apply
    </button>
    <button
        v-if="couponCode"
        @click.prevent="removeDiscount"
        class="absolute top-[14px] right-4 text-lg text-vivid-purple"
    >
      Delete
    </button>
   <span v-if="promorErr.length" class="text-red-500 text-xs">{{ promorErr }}</span>
   <span v-if="promoText.length" class="text-green-500 text-xs">{{ promoText }}</span>
  </div>
  <h5 class="text-[19px]/[22px] sm:text-[27px]/[31px] font-semibold mt-[17px] mb-[13px]">
    Payment information
  </h5>
  <div class="flex flex-col relative">
    <div class="mb-4">
      <CustomField name="cartName" :rules="[required]" placeholder="Name on card" />
      <span class="text-red-500 text-xs pt-1">{{ errors.cartName }}</span>
    </div>
  </div>
  <div class="w-full border rounded-small shadow-gray pt-4 pb-[13px] px-[18px] text-base sm:text-xl border-charcoal placeholder:text-silver h-[49px] sm:h-[53px]">
    <div class="" id="stripe-card-number"></div>
  </div>
  <div
  class="flex flex-col justify-between bg-lavender rounded-small py-[14px] px-[22px] mt-6">
    <div class="flex justify-between">
      <span class="text-base sm:text-xl/6">
        <template v-if="postcode">Total Payment ${{ Number(calculateTotal()).toFixed(2) }}</template>
        <template v-else>Estimated Payment ${{ Number(calculateTotal()).toFixed(2) }}</template>
      </span>
      <button @click.prevent="toggleViewMore" class="text-[15px] text-vivid-purple font-raleway">
        {{ !viewMore ? 'View Details' : 'Hide Details' }}
      </button>
    </div>
    <div v-if="viewMore" class="mt-4">
      <div class="flex flex-col gap-3">
        <div v-if="calculateCycle()" class="flex justify-between text-base items-center">
          <span>Vinderkind Subscription</span>
          <span>${{ Number(calculateCycle()).toFixed(2) }}</span>
        </div>
        <div v-if="getCarts().length">
          <div v-for="(item, index) in getCarts()" :key="index">
            <div class="flex justify-between text-base items-center">
              <span>{{ item.name }}</span>
              <div v-if="item.compare_price > item.price" style=" direction: ltr; " >
                <span class="text-base font-bold font-sans">${{ Number(item.price).toFixed(2) }}</span>
                <span class="line-through text-sm font-bold font-sans m-[5px]" style="text-decoration-line: line-through;">${{ Number(item.compare_price).toFixed(2) }}</span>
              </div>
              <span v-else class="text-base font-bold font-sans">${{ Number(item.price).toFixed(2) }}</span>
            </div>
          </div>
        </div>

        <div v-if="calculateShipping()">
          <div class="flex justify-between text-base items-center">
            <span>Shipping</span>
            <span>${{ Number(calculateShipping()).toFixed(2) }}</span>
          </div>
        </div>
        <div v-if="!calculateShipping() && page === 'checkout'">
          <div class="flex justify-between text-base items-center">
            <span>Shipping</span>
            <span>Free</span>
          </div>
        </div>

        <div v-if="calculateTax()">
            <div class="flex justify-between text-base items-center">
              <span>Tax</span>
              <span>${{ Number(calculateTax()).toFixed(2) }}</span>
            </div>
        </div>

        <div v-if="calculateDiscount()">
            <div class="flex justify-between text-base items-center">
              <span>Discount</span>
              <span>-${{ Number(calculateDiscount()).toFixed(2) }}</span>
            </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import {defineComponent, ref, computed, onMounted, watch} from 'vue'
import IconCoupon from '@components/icons/IconCoupon.vue'
import { Field } from 'vee-validate'
import CustomField from '@components/reusable/CustomField.vue'
import { required } from '@utills/helpers/validation'
import { useTotalPaymentStore } from '@stores/totalPayment'
import { useSignUpCartsStore } from '@stores/sign-up-cart'
import { useCartsStore } from '@stores/carts'
import {useStripe} from "@stores/stripe";

import cardLogo from '@utills/helpers/constant'
import type {IResponseDiscount} from "@utills/types";

export default defineComponent({
  components: { IconCoupon, Field, CustomField },
  data() {
    return {
      expiryDate: '',
    }
  },
  props: {
    errors: {
      type: Object,
      required: true
    },
    page: {
      type: String,
    },
    couponCode: {
      type: String
    }
  },
  setup(props) {
    const storeStripe = useStripe();


    onMounted(() => {
      storeStripe.initialElement('#stripe-card-number')
      if(props.couponCode){
        promo.value = props.couponCode
        isDivVisible.value = true
      }
    })

    watch(() => props.couponCode, () => {
      if(props.couponCode){
        promo.value = props.couponCode
        isDivVisible.value = true
      }
    })



    let func = props.page == 'checkout' ? useCartsStore() : useSignUpCartsStore();
    const { calculateDiscount, calculateCycle, calculateShipping, calculateTax, calculateTotal, getCarts, setDiscount, unsetDiscount,postcode } = func

    const cardValidation = (value: string): boolean | string => {
      const cleanedValue = value.replace(/\D/g, '')
      const regex = /^\d{15,16}$/
      return regex.test(cleanedValue) ? true : 'Invalid card'
    }
    const cardNumber = ref('')

    const isDivVisible = ref(false)
    const viewMore = ref(false)
    const promo = ref('')
    const promorErr = ref('')
    const promoText = ref('')




    function toggleDivVisibility() {
      isDivVisible.value = !isDivVisible.value
    }

    const applyDiscount = () =>  {
      setDiscount(promo.value)
        .then(function (response: IResponseDiscount) {
          if(response.err) {
            promorErr.value = 'Not a valid Promo Code'
            promoText.value = ''
          } else {
            promoText.value = '$' + Number(response.data.discountTotal).toFixed(2) + ' Discount Applied'
            promorErr.value = ''
          }
        })
    }

    const removeDiscount = () => {
      unsetDiscount()
          .then(function () {
              promorErr.value = ''
              promoText.value = ''
              promo.value = ''
          })
    }

    const { calculateTotalAmount, totalPayment, calculateProduct, calculateZip } =
      useTotalPaymentStore()
    function toggleViewMore() {
      viewMore.value = !viewMore.value
    }
    const isFocused = ref(false)

    function focusField() {
      isFocused.value = true
    }

    const phoneNumber = ref('')

    const cardLogoUrl = computed(() => {
      const number = cardNumber.value.replace(/\D/g, '')
      for (const card of cardLogo) {
        if (number.match(card.re)) {
          return card.logo
        }
      }
      return ''
    })

    const cardMask = computed(() => {
      const number = cardNumber.value.replace(/\D/g, '')
      for (const card of cardLogo) {
        if (number.match(card.re)) {
          return card?.mask ? card.mask : '#### #### #### ####'
        }
      }
      return '#### #### #### ####'
    })

    return {
      postcode,
      isDivVisible,
      promo,
      promorErr,
      promoText,
      toggleDivVisibility,
      required,
      viewMore,
      toggleViewMore,
      focusField,
      isFocused,
      applyDiscount,
      removeDiscount,
      calculateTotalAmount,
      totalPayment,
      calculateProduct,
      calculateZip,
      calculateTax,
      calculateShipping,
      calculateDiscount,
      calculateTotal,
      cardValidation,
      calculateCycle,
      getCarts,
      setDiscount,
      phoneNumber,
      cardNumber,
      cardLogoUrl,
      cardMask
    }
  }
})
</script>
