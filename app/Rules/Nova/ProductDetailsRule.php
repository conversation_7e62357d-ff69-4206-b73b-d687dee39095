<?php

namespace App\Rules\Nova;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class ProductDetailsRule implements ValidationRule
{
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if(request()->input('status') === "published") {
            collect(json_decode($value, true))
            ->reject(function ($value, $key) {
                return in_array($key, ['Compare Price']);
            })
            ->each(function ($value) use ($fail) {
                if (!is_numeric($value)) {
                    $fail("validation.numeric")->translate();
                }
                if (empty($value) || $value <= 0) {
                    $fail("validation.details")->translate();
                }
            });
        }
    }
}
