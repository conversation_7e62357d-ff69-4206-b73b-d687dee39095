<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdatePaymentMethodRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth('customers')->check();
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'paymentInfo' => 'required|array',
            'subscription_id' => 'required|integer|exists:subscriptions,id',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'paymentInfo.required' => 'Payment information is required.',
            'paymentInfo.array' => 'Payment information must be a valid format.',
            'subscription_id.required' => 'Subscription ID is required.',
            'subscription_id.integer' => 'Subscription ID must be a valid number.',
            'subscription_id.exists' => 'The selected subscription does not exist.',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            // Check if the subscription belongs to the authenticated user
            $user = auth('customers')->user();
            $subscriptionId = $this->input('subscription_id');
            
            if ($user && $subscriptionId) {
                $subscription = $user->subscriptions()->where('id', $subscriptionId)->first();
                if (!$subscription) {
                    $validator->errors()->add('subscription_id', 'You do not have permission to update this subscription.');
                }
            }
        });
    }
}
