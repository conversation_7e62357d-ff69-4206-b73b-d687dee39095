<?php

namespace App\Http\Controllers;

use App\Customer;
use App\Http\Requests\Auth\RenewSubscriptionRequest;
use App\Http\Requests\Auth\UpdateRequest;
use App\Http\Requests\Auth\UpdateSubscriptionRequest;
use App\Http\Requests\Code\GetRequest;
use App\Http\Requests\UpdatePaymentMethodRequest;
use App\Http\Requests\Code\VerifyRequest;
use App\Jobs\Subscription\ResumeJob;
use App\Services\Checkout\Checkout;
use App\Subscription;
use App\Services\NotificationService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class SignInController extends Controller
{
    private NotificationService $notificationService;

    public function __construct(NotificationService $notificationService)
    {
        $this->notificationService = $notificationService;
    }

    public function getCode(GetRequest $request): void
    {
        $code = generateCode();

        Log::channel('auth')->info('Make code: ' . $code);

        $type = $request->input('type');
        $value = $type == 'phone' ? parseCell($request->input('value')) : $request->input('value');
        $customer = Customer::query()->where($type, $value)->first();

        if (!$customer) {
            Log::channel('auth')->error('Customer not found: ' . $value);

            abort(422, 'Customer not found');
        }

        $responseSent = $this->notificationService->verificationAccount($customer, $code);

        if (!$responseSent->success) {
            Log::error("Code not sent: \nMessage: $responseSent->message \nCustomer: $customer->id");

            abort(422, 'Code not sent!');
        }

        request()->session()->put(
            'log_in',
            ['user' => $customer->id, 'code' => $code, 'time' => now()->toDateTimeString()]
        );
    }

    /**
     * @param VerifyRequest $request
     * @return JsonResponse
     */
    public function verify(VerifyRequest $request): JsonResponse
    {
        $auth = $request->session()->get('log_in');
        $authCode = data_get($auth, 'code');
        if ($request->input('code') != $authCode) {
            Log::channel('auth')->error("Wrong code: enter - {$request->input('code')} != user - $authCode");
            return response()->json([
                'success' => false,
                'message' => 'Wrong code'
            ], 422);
        }
        if (!now()->subMinutes(5)->isBefore(now()->parse(data_get($auth, 'time')))) {
            Log::channel('auth')->error("Wrong time: enter - {$request->input('code')} != user - $authCode");
            return response()->json([
                'success' => false,
                'message' => 'Wrong time'
            ], 422);
        }

        $customer = Customer::query()->find(data_get($auth, 'user'));

        $token = Str::random(60);

        $customer->forceFill([
            'api_token' => hash('sha256', $token),
        ])->save();

//        Auth::guard('customers')->setToken($token);

        return response()
            ->json([
                'token' => $token,
                'data' => $customer->frontEnd
            ])
            ->header('authorization', $token)
            ->header('Access-Control-Expose-Headers', 'Authorization');
    }

    public function user(Request $request): JsonResponse
    {
        return response()->json($request->user());
    }

    public function getSubscription($id, Request $request): JsonResponse
    {
        $subscription = $request->user()->subscriptions()->where('id', $id)->firstOrFail();

        return response()->json($subscription->frontEnd);
    }

    public function updateSubscription($id, UpdateSubscriptionRequest $request): JsonResponse
    {
        $subscription = $request->user()->subscriptions()->where('id', $id)->firstOrFail();

        $subscription->update($request->only(['name', 'address_line_1', 'address_line_2', 'city', 'state', 'postal_code']));

        return response()->json([
            'success' => true,
            'message' => 'Subscription updated!'
        ]);

    }
    public function getSubscriptions(Request $request): JsonResponse
    {
        $user = $request->user();
        $subscriptions = $user
            ->subscriptions
            ->sortByDesc('id')
            ->values()
            ->map(function($item){
                return $item->frontEnd;
            });

        return response()->json($subscriptions);
    }

    public function renewSubscription(RenewSubscriptionRequest $request): JsonResponse
    {
        ResumeJob::dispatch($request->input('id'), resource: 'auto-renew-customer');

        return response()->json([
            'success' => true,
            'message' => 'Success Renew Subscription'
        ]);
    }

    public function update(UpdateRequest $request): JsonResponse
    {
        Auth::guard('customers')->user()->update($request->only(['name', 'email', 'phone','preferred_contact_method']));

        return response()->json([
            'user' => Auth::guard('customers')->user(),
            'message' => 'Success Update User'
        ]);
    }

    public function updatePaymentMethod(UpdatePaymentMethodRequest $request): JsonResponse
    {
        $user = $request->user();

        // Get the subscription (validation already ensures it belongs to the user)
        $subscription = $user->subscriptions()->where('id', $request->input('subscription_id'))->firstOrFail();

        try {
            // Get payment driver based on environment configuration
            $paymentDriver = \Lunar\Facades\Payments::driver(config('lunar.payments.default'));

            // Create new credit card token
            $creditCardData = $paymentDriver->getToken(
                customer: $user,
                payment: $request->input('paymentInfo'),
                withoutCard: false
            );

            if (!$creditCardData || !isset($creditCardData['creditCardId'])) {
                Log::error('Payment method update failed: Invalid credit card data returned');
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to process payment method. Please check your card details and try again.'
                ], 422);
            }

            // Get the new credit card record
            $newCreditCard = \App\CreditCard::find($creditCardData['creditCardId']);

            if (!$newCreditCard) {
                Log::error('Payment method update failed: Credit card not found with ID: ' . $creditCardData['creditCardId']);
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to save payment method. Please try again.'
                ], 422);
            }

            // Store the old credit card ID for potential cleanup
            $oldCreditCardId = $subscription->credit_card_id;

            // Update the subscription to use the new credit card
            $subscription->update([
                'credit_card_id' => $newCreditCard->id
            ]);

            Log::info("Payment method updated successfully for subscription {$subscription->id}. Old card: {$oldCreditCardId}, New card: {$newCreditCard->id}");

            return response()->json([
                'success' => true,
                'message' => 'Payment method updated successfully',
                'credit_card' => $newCreditCard->frontEnd
            ]);

        } catch (\Lunar\Exceptions\PaymentException $e) {
            Log::error('Payment method update failed - Payment Exception: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Payment processing failed: ' . $e->getMessage()
            ], 422);

        } catch (\Exception $e) {
            Log::error('Payment method update failed - General Exception: ' . $e->getMessage());
            Log::error('Stack trace: ' . $e->getTraceAsString());

            return response()->json([
                'success' => false,
                'message' => 'An unexpected error occurred. Please try again or contact support if the problem persists.'
            ], 500);
        }
    }

    public function logout(): void
    {
        Auth::guard('customers')->logOut();
    }
}
