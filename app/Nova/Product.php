<?php

namespace App\Nova;

use App\Rules\Nova\ProductDetailsRule;
use Ebess\AdvancedNovaMediaLibrary\Fields\Images;
use <PERSON>vel\Nova\Fields\ID;
use <PERSON><PERSON>\Nova\Fields\Text;
use <PERSON><PERSON>\Nova\Fields\Boolean;
use <PERSON><PERSON>\Nova\Fields\KeyValue;
use <PERSON><PERSON>\Nova\Fields\Select;
use <PERSON><PERSON>\Nova\Fields\Badge;
use Laravel\Nova\Http\Requests\NovaRequest;
use Illuminate\Database\Eloquent\Model;
use Outl1ne\NovaSortable\Traits\HasSortableRows;

class Product extends Resource
{
    use HasSortableRows{
        indexQuery as indexSortableQuery;
    }
    /**
     * The model the resource corresponds to.
     *
     * @var class-string<\App\Product>
     */
    public static $model = \App\Product::class;

    public static $group = 'Store';

    public static $showpollingtoggle = true;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'name';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id',
        'attribute_data->name->value',
    ];

    public static function indexQuery(NovaRequest $request, $query)
    {
        return parent::indexQuery($request, static::indexSortableQuery($request, $query->where('product_type_id', \App\Product::$productTypes['accessories'])));
    }

    /**
     * Get the fields displayed by the resource.
     *
     * @param NovaRequest $request
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            ID::make()->sortable(),

            Text::make('Name'),

            Select::make('Status')
                ->default('published')
                ->options([
                    'published' => 'Published',
                    'draft' => 'Draft',
                ])
                ->onlyOnForms(),

            Badge::make('Status')->map([
                'draft' => 'danger',
                'published' => 'success',
            ]),

            Images::make('Images'),

            KeyValue::make('Details', 'variantJson')
                ->rules('required', new ProductDetailsRule)
                ->disableEditingKeys()
                ->disableAddingRows()
                ->disableDeletingRows()
                ->resolveUsing(function () {
                    return [
                        'Price' => null,
                        'Compare Price' => null,
                        'Length' => null,
                        'Width' => null,
                        'Height' => null,
                        'Weight' => null,
                    ];
                })
                ->onlyOnForms()
                ->hideWhenUpdating(),

            KeyValue::make('Details', 'variantJson')
                ->rules('required', new ProductDetailsRule)
                ->disableEditingKeys()
                ->disableAddingRows()
                ->disableDeletingRows()
                ->resolveUsing(function () {
                    $data = $this->variantJson;
                    return [
                        'Price' => $this->price,
                        'Compare Price' => $this->compare_price,
                        'Length' => $data['length'],
                        'Width' => $data['width'],
                        'Height' => $data['height'],
                        'Weight' => $data['weight'],
                    ];
                })
                ->hideWhenCreating(),

            Boolean::make('Show On Subscription Signup', 'show_on_signup'),
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @param NovaRequest $request
     * @return array
     */
    public function cards(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param NovaRequest $request
     * @return array
     */
    public function filters(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param NovaRequest $request
     * @return array
     */
    public function lenses(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param NovaRequest $request
     * @return array
     */
    public function actions(NovaRequest $request)
    {
        return [];
    }

    public static function afterCreate(NovaRequest $request, Model $model)
    {
        $data = json_decode($request->variantJson, true);

        $variant = $model->variants()->create([
            'tax_class_id' => 1,

            'length_value' => $data['Length'],
            'length_unit' => 'in',

            'width_value' => $data['Width'],
            'width_unit' => 'in',

            'height_value' => $data['Height'],
            'height_unit' => 'in',

            'weight_value' => $data['Weight'],
            'weight_unit' => 'oz',

            'sku' => 'product_' . $model->id,
        ]);

        $variant->prices()->create([
            'price' => empty($data['Price']) ? 0 : (float)$data['Price'] * 100,
            'compare_price' => empty($data['Compare Price']) ? 0 : (float)$data['Compare Price'] * 100,
            'currency_id' => 1
        ]);

        $model->update([
            'attribute_data' => collect([
                'name' => new \Lunar\FieldTypes\Text($request->name),
            ]),
        ]);
    }

    public static function afterUpdate(NovaRequest $request, Model $model)
    {
        $data = json_decode($request->variantJson, true);

        $variant = $model->variants()->first();

        $variant->update([
            'length_value' => $data['Length'] ?? null,

            'width_value' => $data['Width'] ?? null,

            'height_value' => $data['Height'] ?? null,

            'weight_value' => $data['Weight'] ?? null,
        ]);

        $variant->prices->first()->update([
            'price' => empty($data['Price']) ? 0 : (float)$data['Price'] * 100,
            'compare_price' => empty($data['Compare Price']) ? 0 : (float)$data['Compare Price'] * 100,
        ]);

        $model->update([
            'attribute_data' => collect([
                'name' => new \Lunar\FieldTypes\Text($request->name),
            ]),
        ]);
    }

    public static function usesScout()
    {
        return false;
    }
}
